/* تنسيقات الصفحة الرئيسية المحسنة */

/* قسم البطل المحسن */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/1.webp') repeat;
    opacity: 0.1;
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 50px;
}

.hero-content {
    flex: 1;
    color: var(--text-light);
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 25px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-badge i {
    color: var(--secondary-color);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-title .highlight {
    color: var(--secondary-color);
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.hero-description {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
}

.feature-item i {
    color: var(--secondary-color);
    font-size: 1.2rem;
}

.hero-image {
    flex: 1;
    position: relative;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.image-container img {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
}

.floating-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.floating-card h4 {
    color: var(--dark-color);
    margin-bottom: 5px;
}

.floating-card p {
    color: #666;
    font-size: 0.9rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* قسم الإحصائيات */
.stats-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--bg-light) 0%, #fff 100%);
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-card {
    background: var(--light-color);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.stat-icon i {
    font-size: 2rem;
    color: var(--text-light);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.stat-card .stat-label {
    font-size: 1.1rem;
    color: #666;
}

/* قسم شهادات المستخدمين */
.testimonials-section {
    padding: 80px 0;
    background: var(--dark-color);
    color: var(--text-light);
}

.testimonials-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.testimonial-content {
    margin-bottom: 25px;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    font-style: italic;
    position: relative;
}

.testimonial-content p::before {
    content: '"';
    font-size: 3rem;
    color: var(--secondary-color);
    position: absolute;
    top: -10px;
    right: -10px;
    opacity: 0.5;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-avatar i {
    font-size: 1.5rem;
    color: var(--text-light);
}

.author-info h4 {
    margin-bottom: 5px;
    color: var(--secondary-color);
}

.author-info p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.testimonial-rating {
    display: flex;
    gap: 5px;
}

.testimonial-rating i {
    color: var(--secondary-color);
    font-size: 1.2rem;
}

/* تحسين قسم آخر المحتويات */
.content-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 40px;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    font-size: 1.1rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    color: #666;
}

.tab-btn.active,
.tab-btn:hover {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.content-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: var(--text-light);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.content-stats {
    display: flex;
    gap: 15px;
    margin: 15px 0;
    color: #666;
    font-size: 0.9rem;
}

.content-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* تحسين بطاقات المحتوى */
.content-card {
    background: var(--light-color);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.content-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.content-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.content-card:hover .content-image img {
    transform: scale(1.05);
}

.content-info {
    padding: 25px;
}

.content-info h3 {
    color: var(--dark-color);
    margin-bottom: 15px;
    font-size: 1.3rem;
    line-height: 1.4;
}

.content-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
    color: #666;
    font-size: 0.9rem;
}

.content-meta span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.content-excerpt {
    line-height: 1.6;
    margin-bottom: 20px;
    color: #555;
}

.read-more {
    color: var(--primary-color);
    font-weight: bold;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.read-more:hover {
    color: var(--secondary-color);
    transform: translateX(-5px);
}

.read-more::after {
    content: '←';
    transition: transform 0.3s ease;
}

.read-more:hover::after {
    transform: translateX(-3px);
}
