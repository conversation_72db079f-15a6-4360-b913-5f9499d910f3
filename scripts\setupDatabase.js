const { pool, createDatabase, testConnection } = require('../config/database');
const {
    createUsersTable,
    createCategoriesTable,
    createScholarsTable,
    createFatwasTable,
    createSermonsTable,
    createLecturesTable,
    createThinkersTable,
    createNewsletterTable,
    createQuestionsTable
} = require('../config/createTables');

// دالة لإنشاء جميع الجداول
async function createAllTables() {
    try {
        console.log('🚀 بدء إنشاء الجداول...');

        // إنشاء الجداول بالترتيب الصحيح (مراعاة العلاقات)
        const tables = [
            { name: 'users', sql: createUsersTable },
            { name: 'categories', sql: createCategoriesTable },
            { name: 'scholars', sql: createScholarsTable },
            { name: 'fatwas', sql: createFatwasTable },
            { name: 'sermons', sql: createSermonsTable },
            { name: 'lectures', sql: createLecturesTable },
            { name: 'thinkers', sql: createThinkersTable },
            { name: 'newsletter_subscribers', sql: createNewsletterTable },
            { name: 'questions', sql: createQuestionsTable }
        ];

        for (const table of tables) {
            await pool.execute(table.sql);
            console.log(`✅ تم إنشاء جدول: ${table.name}`);
        }

        console.log('🎉 تم إنشاء جميع الجداول بنجاح!');
    } catch (error) {
        console.error('❌ خطأ في إنشاء الجداول:', error.message);
        throw error;
    }
}

// دالة لإدراج البيانات الأولية
async function insertInitialData() {
    try {
        console.log('📝 إدراج البيانات الأولية...');

        // إدراج التصنيفات الأساسية
        const categories = [
            { name: 'العقيدة', slug: 'aqeedah', type: 'sermon' },
            { name: 'الفقه', slug: 'fiqh', type: 'sermon' },
            { name: 'الأخلاق', slug: 'akhlaq', type: 'sermon' },
            { name: 'السيرة النبوية', slug: 'seerah', type: 'sermon' },
            { name: 'المناسبات', slug: 'occasions', type: 'sermon' },
            { name: 'العبادات', slug: 'worship', type: 'fatwa' },
            { name: 'المعاملات', slug: 'transactions', type: 'fatwa' },
            { name: 'الأسرة والزواج', slug: 'family', type: 'fatwa' },
            { name: 'قضايا معاصرة', slug: 'contemporary', type: 'fatwa' },
            { name: 'الأخلاق والآداب', slug: 'ethics', type: 'fatwa' }
        ];

        for (const category of categories) {
            await pool.execute(
                'INSERT IGNORE INTO categories (name, slug, type, description) VALUES (?, ?, ?, ?)',
                [category.name, category.slug, category.type, `تصنيف ${category.name}`]
            );
        }

        console.log('✅ تم إدراج التصنيفات الأساسية');

        // إنشاء مستخدم إداري افتراضي
        const bcrypt = require('bcryptjs');
        const adminPassword = await bcrypt.hash('admin123', 12);
        
        await pool.execute(
            'INSERT IGNORE INTO users (username, email, password, full_name, role, is_verified) VALUES (?, ?, ?, ?, ?, ?)',
            ['admin', '<EMAIL>', adminPassword, 'مدير النظام', 'admin', true]
        );

        console.log('✅ تم إنشاء المستخدم الإداري الافتراضي');
        console.log('📧 البريد الإلكتروني: <EMAIL>');
        console.log('🔑 كلمة المرور: admin123');

        console.log('🎉 تم إدراج جميع البيانات الأولية بنجاح!');
    } catch (error) {
        console.error('❌ خطأ في إدراج البيانات الأولية:', error.message);
        throw error;
    }
}

// دالة رئيسية لإعداد قاعدة البيانات
async function setupDatabase() {
    try {
        console.log('🔧 بدء إعداد قاعدة البيانات...');
        
        // إنشاء قاعدة البيانات
        await createDatabase();
        
        // اختبار الاتصال
        await testConnection();
        
        // إنشاء الجداول
        await createAllTables();
        
        // إدراج البيانات الأولية
        await insertInitialData();
        
        console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
        process.exit(0);
    } catch (error) {
        console.error('❌ فشل في إعداد قاعدة البيانات:', error.message);
        process.exit(1);
    }
}

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    setupDatabase();
}

module.exports = { setupDatabase, createAllTables, insertInitialData };
